<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="713ce200-e8b6-46e7-b0b8-e8c552a10a1c" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/core/src/main/java/com/hzy/core/utils/ThirtySevenPayUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/hzy/core/utils/support/JacksonUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/hzy/core/utils/support/JacksonUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2wqrNLi2eOOagvNng02BpNsNqXW" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.kita [clean,compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.kita [clean,package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.kita [clean].executor&quot;: &quot;Run&quot;,
    &quot;Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;找到数据库连接形参&quot;,
    &quot;Notification.DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.AdminApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.ClientApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.WebsocketApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;naifu&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Documents/凌越科技项目代码/心选语音/xinxuan1v1-server&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvnd clean package" />
      <command value="mvn clean package" />
      <command value="mvn clean compile" />
      <command value="mvn clean" />
      <command value="mvnd compile" />
      <command value="mvnd clean" />
    </option>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.ClientApplication">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="xinxuan1v1-server" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="xinxuan1v1-server" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="admin" />
      <option name="PROGRAM_PARAMETERS" value="--spring.profiles.active=dev" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hzy.AdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ClientApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="client" />
      <option name="PROGRAM_PARAMETERS" value="--spring.profiles.active=dev" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hzy.ClientApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WebsocketApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="websocket" />
      <option name="PROGRAM_PARAMETERS" value="--spring.profiles.active=dev" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hzy.WebsocketApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.AdminApplication" />
      <item itemvalue="Spring Boot.ClientApplication" />
      <item itemvalue="Spring Boot.WebsocketApplication" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26094.121" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-IU-251.26094.121" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="实体" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="713ce200-e8b6-46e7-b0b8-e8c552a10a1c" name="更改" comment="" />
      <created>1746779903960</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746779903960</updated>
      <workItem from="1746779905054" duration="575000" />
      <workItem from="1746849542164" duration="1202000" />
      <workItem from="1746858651628" duration="1494000" />
      <workItem from="1746861077250" duration="16000" />
      <workItem from="1746963300455" duration="4042000" />
      <workItem from="1747020945808" duration="3342000" />
      <workItem from="1747221741072" duration="240000" />
      <workItem from="1747222131475" duration="71000" />
      <workItem from="1747399651597" duration="645000" />
      <workItem from="1747554842318" duration="664000" />
      <workItem from="1747719820024" duration="248000" />
      <workItem from="1747723460319" duration="154000" />
      <workItem from="1750493056931" duration="278000" />
      <workItem from="1751705976112" duration="828000" />
      <workItem from="1752306023275" duration="6867000" />
      <workItem from="1752416657691" duration="8871000" />
      <workItem from="1752480142545" duration="13895000" />
      <workItem from="1752539224561" duration="1698000" />
      <workItem from="1752563872905" duration="748000" />
      <workItem from="1752589668458" duration="3696000" />
      <workItem from="1752650739708" duration="6801000" />
      <workItem from="1752716030895" duration="1195000" />
      <workItem from="1752759851582" duration="6000" />
      <workItem from="1752759864127" duration="14280000" />
      <workItem from="1752821371029" duration="11000" />
      <workItem from="1752830394542" duration="2508000" />
      <workItem from="1752834155679" duration="3850000" />
      <workItem from="1752841389843" duration="90000" />
      <workItem from="1752841566540" duration="8720000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>